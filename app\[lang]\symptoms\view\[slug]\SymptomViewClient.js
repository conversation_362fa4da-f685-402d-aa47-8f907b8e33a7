'use client';

import {  Accordion, AccordionSummary, AccordionDetails } from '@mui/material';
import NavBar from '../../../../components/NavBar';

import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ContentSection from '../../../../components/ContentSection';
import ShareButton from '../../../../components/shared/ShareButton';
import { Box, Container, Typography, Breadcrumbs, Link as MuiLink } from '@mui/material';
import Link from 'next/link';
import Footer from '../../../../components/Footer';
import { Helmet } from 'react-helmet-async';
import { getRedirectPath } from '@/app/utils/getRedirectPath';
import Widget from '@/app/components/Widget';
import AskQuestions from '@/app/components/AskQuestions';
import AskQuestionsBanner from '@/app/components/AskQuestionsBanner';


function stripBodyTag(html) {
  if (!html) return "";
  const match = html.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
  return match ? match[1] : html;
}
function extractFirstH1(html) {
  if (!html) return null;
  // Use regex to find the first h1 tag and extract its content
  const h1Match = html.match(/<h1[^>]*>(.*?)<\/h1>/i);
  if (h1Match) {
    // Remove any HTML tags from the content and trim whitespace
    return h1Match[1].replace(/<[^>]*>/g, "").trim();
  }
  return null;
}

// Helper to parse and split FAQ section using regex (server-safe)
function extractFAQandRest(html) {
  if (!html) return { restHtml: "", faq: [] };

  // Find FAQ section using regex
  const faqRegex =
    /<h[1-6][^>]*>\s*frequently\s+asked\s+questions[^<]*<\/h[1-6]>/i;
  const faqMatch = html.match(faqRegex);

  if (!faqMatch) {
    return { restHtml: html, faq: [] };
  }

  const faqStartIndex = faqMatch.index;
  const faqHeading = faqMatch[0];

  // Find where FAQ section ends (next h1 or h2, or end of content)
  const afterFaqHtml = html.substring(faqStartIndex + faqHeading.length);
  const endMatch = afterFaqHtml.match(/<h[12][^>]*>/i);
  const faqEndIndex = endMatch
    ? faqStartIndex + faqHeading.length + endMatch.index
    : html.length;

  // Extract the FAQ section content
  const faqSection = html.substring(faqStartIndex, faqEndIndex);

  // Build rest HTML (everything except FAQ section)
  const restHtml =
    html.substring(0, faqStartIndex) + html.substring(faqEndIndex);

  // Parse FAQ questions and answers
  const faq = [];

  // Find all h3-h6 headings in FAQ section (these are questions)
  const questionRegex = /<h[3-6][^>]*>(.*?)<\/h[3-6]>/gi;
  let questionMatch;
  let lastQuestionEnd = faqHeading.length;

  while ((questionMatch = questionRegex.exec(faqSection)) !== null) {
    const question = questionMatch[1].replace(/<[^>]*>/g, "").trim();
    const questionStart = questionMatch.index;
    const questionEnd = questionMatch.index + questionMatch[0].length;

    // Find the next question or end of FAQ section
    const nextQuestionMatch = questionRegex.exec(faqSection);
    const answerEnd = nextQuestionMatch
      ? nextQuestionMatch.index
      : faqSection.length;

    // Reset regex lastIndex to continue from where we left off
    questionRegex.lastIndex = questionEnd;

    // Extract answer content between this question and next question
    const answerHtml = faqSection.substring(questionEnd, answerEnd).trim();

    if (question) {
      faq.push({
        question: question,
        answerHtml: answerHtml,
      });
    }
  }

  return { restHtml, faq };
}

export default function SymptomViewClient({
  symptom,
  language,
  langStrings,
  metaTitle,
  metaDescription,
  error
}) {
   let firstHeading = "";
  let restHtml = '';
  let faq = [];
  function removeFirstH1(html) {
    if (!html) return html;
    return html.replace(/<h1[^>]*>.*?<\/h1>/i, "");
  }
  if (symptom && symptom.body_html) {
    const { restHtml: r, faq: f } = extractFAQandRest(
      stripBodyTag(symptom.body_html)
    );
    restHtml = removeFirstH1(r);
    faq = f;
    firstHeading = extractFirstH1(stripBodyTag(symptom.body_html));
  }
  if (error || !symptom) {
    return (
      <div>
        <NavBar />
        <Box sx={{ backgroundColor: '#F7F6F4', py: 4 }}>
          <Container>
            <Typography variant="h4">
              {error || langStrings.symptomNotFound}
            </Typography>
          </Container>
        </Box>
        <Footer />
      </div>
    );
  }

  return (
    <div>
      <Helmet>
        <title>{metaTitle}</title>
        <meta name="description" content={metaDescription} />
        <meta property="og:title" content={metaTitle} />
        <meta property="og:description" content={metaDescription} />
      </Helmet>
      <NavBar />

      {/* Header Section */}
      {/* <Box sx={{ backgroundColor: "#F7F6F4", py: 6 }}>
        <Container maxWidth="lg">
          {/* Breadcrumbs 
          <Breadcrumbs sx={{ mb: 3 }}>
            <Link
              href={getRedirectPath(`/${language}`)}
              passHref
              style={{ textDecoration: "none" }}
            >
              <MuiLink color="inherit" underline="hover">
                {langStrings.home}
              </MuiLink>
            </Link>
            <Link
              href={getRedirectPath(`/${language}/symptoms`)}
              passHref
              style={{ textDecoration: "none" }}
            >
              <MuiLink color="inherit" underline="hover">
                {langStrings.title}
              </MuiLink>
            </Link>
            <Typography color="text.primary">{symptom.name}</Typography>
          </Breadcrumbs>

          {/* Title and Description 
          <Typography
            variant="h2"
            component="h1"
            gutterBottom
            sx={{ fontWeight: "bold", mb: 2 }}
          >
            {symptom.name}
          </Typography>
          {symptom.definition && (
            <Typography
              variant="h6"
              sx={{
                color: "text.secondary",
                mb: 2,
                maxWidth: "800px",
              }}
            >
              {symptom.definition}
            </Typography>
          )}

          {/* Share Button 
          <Box sx={{ mb: 4 }}>
            <ShareButton copiedText={"Link copied to clipboard"} />
          </Box>
        </Container>
      </Box> */}

      {/* Main Content */}
      <AskQuestionsBanner />
      <Box sx={{ pb: 6, pt: 3 }}>
        <Container maxWidth="lg">
          {firstHeading && (
            <Box
              sx={{
                maxWidth: "800px",
                wordWrap: "break-word",
                margin: "0 auto",
                overflowWrap: "break-word",
                "& *": {
                  wordWrap: "break-word",
                  overflowWrap: "break-word",
                  hyphens: "auto",
                },
              }}
            >
              <Typography
                variant="h1"
                sx={{
                  // marginTop: "2rem",
                  // marginBottom: "1rem",
                  fontSize: "2.2rem",
                  fontWeight: 700,
                  display: "flex",
                  alignItems: "flex-start",
                }}
              >
                {firstHeading}
              </Typography>
            </Box>
          )}
          {/* Author Created At Breadcrumbs */}
          <Box
            sx={{
              maxWidth: "800px",
              margin: "0 auto",
              mb: 2,
              display: "flex",
              alignItems: "center",
            }}
          >
            <Breadcrumbs
              style={{
                display: "flex",
                maxWidth: "640px",
                width: "100%",
                alignItems: "center",
                minHeight: "50px",
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <ShareButton
                  copiedText={
                    langStrings?.linkCopied || "Link copied to clipboard"
                  }
                />
                {/* <Typography sx={{ textDecoration: "none", color: "inherit" }}>
                            Author: John Doe
                          </Typography> */}
              </Box>
            </Breadcrumbs>
            <Typography
              style={{
                textDecoration: "none",
                color: "#00000099",
                display: "flex",
                width: "100%",
                maxWidth: "160px",
              }}
              color="text.primary"
            >
              Created at:
              {new Date(symptom.meta.created_at).toLocaleDateString()}
            </Typography>
          </Box>

          <Widget />
          <Box sx={{ maxWidth: "800px", margin: "0 auto" }}>
            {/* <ContentSection body_html={symptom.sections} /> */}
            {symptom && (
              <>
                <Box
                  sx={{
                    "& img": {
                      maxWidth: "100%",
                      height: "auto",
                      borderRadius: "4px",
                      display: "block",
                      margin: "1rem auto",
                    },
                    "& table": {
                      width: "100%",
                      borderCollapse: "collapse",
                      marginBottom: "2rem",
                      "& th, & td": {
                        border: "1px solid #ddd",
                        padding: "12px",
                        textAlign: "left",
                      },
                      "& th": {
                        backgroundColor: "#f5f5f5",
                        fontWeight: 600,
                      },
                    },
                    "& p": {
                      marginBottom: "1rem",
                      lineHeight: 1.6,
                      fontSize: "1.1rem",
                    },
                    "& h1": {
                      marginTop: "2rem",
                      marginBottom: "1rem",
                      fontSize: "2.2rem",
                      fontWeight: 700,
                    },
                    "& h2": {
                      marginTop: "2rem",
                      marginBottom: "1rem",
                      fontSize: "1.8rem",
                      fontWeight: 600,
                    },
                    "& h3": {
                      marginTop: "1.5rem",
                      marginBottom: "0.75rem",
                      fontSize: "1.5rem",
                      fontWeight: 600,
                    },
                    "& h4": {
                      marginTop: "1.2rem",
                      marginBottom: "0.75rem",
                      fontSize: "1.3rem",
                      fontWeight: 600,
                    },
                    "& ul, & ol": {
                      marginBottom: "1rem",
                      paddingLeft: "2rem",
                      listStyle: "disc",
                      "& li": {
                        marginBottom: "0.5rem",
                        lineHeight: 1.6,
                      },
                    },
                    "& a": {
                      color: "#1976d2",
                      textDecoration: "underline",
                      "&:hover": {
                        textDecoration: "none",
                      },
                    },
                    "& blockquote": {
                      borderLeft: "4px solid #ccc",
                      paddingLeft: "1rem",
                      color: "#555",
                      fontStyle: "italic",
                      margin: "1rem 0",
                    },
                    "& code": {
                      backgroundColor: "#f4f4f4",
                      padding: "0.2em 0.4em",
                      borderRadius: "4px",
                      fontFamily: "monospace",
                      fontSize: "0.95em",
                    },
                    "& pre": {
                      backgroundColor: "#f4f4f4",
                      padding: "1rem",
                      borderRadius: "4px",
                      overflowX: "auto",
                      fontFamily: "monospace",
                      fontSize: "0.95em",
                      marginBottom: "1.5rem",
                    },
                    "& strong": {
                      fontWeight: 700,
                    },
                    "& em": {
                      fontStyle: "italic",
                    },
                    "& hr": {
                      border: 0,
                      borderTop: "1px solid #ccc",
                      margin: "2rem 0",
                    },
                    "& figure": {
                      margin: "1rem auto",
                      textAlign: "center",
                    },
                    "& figcaption": {
                      fontSize: "0.9rem",
                      color: "#666",
                      marginTop: "0.5rem",
                    },
                    "& iframe": {
                      maxWidth: "100%",
                      display: "block",
                      margin: "1.5rem auto",
                    },
                  }}
                >
                  {/* Render the rest of the HTML */}
                  <div dangerouslySetInnerHTML={{ __html: restHtml }} />
                  {/* Render FAQ accordion if present */}
                  {faq.length > 0 && (
                    <Box mt={4}>
                      <Typography variant="h2" mb={2} fontWeight={600}>
                        Frequently asked questions about{" "}
                        {symptom.title || symptom.name || "this"}
                      </Typography>
                      {faq.map((item, idx) => (
                        <Accordion
                          key={idx}
                          sx={{
                            mb: 1,
                            borderRadius: 1,
                            boxShadow: "none",
                            border: "1px solid #e0e0e0",
                            overflow: "hidden",
                            "& .MuiTypography-root": {
                              margin: 1, // Remove default margin
                              lineHeight: 1.3, // Tighter line height
                            },
                          }}
                        >
                          <AccordionSummary
                            mb={0}
                            expandIcon={<ExpandMoreIcon />}
                          >
                            <Typography mb={0} fontWeight={600}>
                              <Box
                                component="span"
                                color="primary.main"
                                fontWeight={700}
                                mr={1}
                                mb={0}
                              >
                                Q{idx + 1}:
                              </Box>
                              {item.question}
                            </Typography>
                          </AccordionSummary>
                          <AccordionDetails>
                            <Box
                              sx={{
                                background: "#f9f9fc",
                                borderRadius: 2,
                                p: 2,
                                border: "1px solid #e3e6f0",
                                boxShadow: "0 1px 2px rgba(0,0,0,0.03)",
                              }}
                            >
                              <div
                                style={{ lineHeight: 1.7, fontSize: "1.08rem" }}
                                dangerouslySetInnerHTML={{
                                  __html: item.answerHtml,
                                }}
                              />
                            </Box>
                          </AccordionDetails>
                        </Accordion>
                      ))}
                    </Box>
                  )}
                </Box>
              </>
            )}
            {symptom.url && (
              <Box
                sx={{
                  mt: 4,
                  pt: 4,
                  borderTop: "1px solid",
                  borderColor: "divider",
                }}
              >
                <Typography variant="body2" color="text.secondary">
                  {langStrings.learnMore}
                  <Link
                    href={symptom.url}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {symptom.url}
                  </Link>
                </Typography>
              </Box>
            )}
          </Box>
        </Container>
      </Box>
      {/* <Widget
        isAtBottom={true}
        title="Want a 1:1 answer for your situation?"
        description="Ask your question privately on August, your 24/7 personal AI health assistant."
      /> */}
      <AskQuestions/>
      <Footer />
    </div>
  );
}
