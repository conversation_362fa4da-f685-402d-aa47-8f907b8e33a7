import React from "react";

const AskQuestionsBanner = () => {
  const handleScrollToWidget = () => {
    const el = document.getElementById("ask-questions-widget");
    if (el) {
      el.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };
  return (
    <div className="max-w-4xl mx-auto px-4">
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          background: "#f6f6f7",
          borderRadius: "10px",
          padding: "14px 16px",
          margin: "20px 0",
          boxSizing: "border-box",
          flexWrap: "wrap",
          gap: "16px",
        }}
      >
        <span
          style={{
            color: "#232a36",
            fontSize: "clamp(1.1rem, 2.5vw, 1.4rem)",
            fontWeight: 500,
            flex: "1 1 auto",
            minWidth: "250px",
          }}
        >
          Question on this topic? Get an instant answer from <b>World's best Ai Doctor</b>.
        </span>
        <button
          onClick={handleScrollToWidget}
          style={{
            background: "#416955",
            color: "#fff",
            border: "none",
            borderRadius: "6px",
            padding: "10px 32px",
            fontSize: "clamp(1.1rem, 2.2vw, 1.3rem)",
            fontWeight: 600,
            cursor: "pointer",
            transition: "background 0.2s",
            flexShrink: 0,
            whiteSpace: "nowrap",
          }}
        >
          Ask Question
        </button>
      </div>
      <style jsx>{`
        @media (max-width: 768px) {
          div[style*="flex"] {
            flex-direction: column !important;
            align-items: stretch !important;
            text-align: center !important;
            padding: 20px 16px !important;
          }
          span {
            margin-bottom: 8px !important;
          }
          button {
            align-self: center !important;
            padding: 12px 24px !important;
          }
        }
        @media (max-width: 480px) {
          div[style*="flex"] {
            padding: 16px 12px !important;
            margin: 16px 0 !important;
          }
          button {
            padding: 10px 20px !important;
            width: 100% !important;
            max-width: 200px !important;
          }
        }
      `}</style>
    </div>
  );
};

export default AskQuestionsBanner;
