'use client';
import { Box, Container, Typography, CircularProgress, Breadcrumbs } from '@mui/material';
import NavBar from '../NavBar';
import BreadcrumbNav from './BreadcrumbNav';
import HeroContent from './HeroContent';
import ErrorView from './ErrorView';
import Footer from './../Footer';
import { Helmet } from 'react-helmet-async';
import Widget from '../Widget';
import ShareButton from './ShareButton';
import AskQuestions from "@/app/components/AskQuestions";
import AskQuestionsBanner from "@/app/components/AskQuestionsBanner";

export default function DetailView({
  loading,
  error,
  data,
  breadcrumbItems,
  children,
  metaTitle,       
  metaDescription,
  langStrings,
  firstHeading
}) {
  if (loading) {
    return (
      <div>
        <NavBar />
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress />
        </Box>
      </div>
    );
  }

  if (error || !data) {
    return <ErrorView message={error} />;
  }
 
  console.log('dataaaaaaaa', data);

  return (
    <div>
      <Helmet>
        <title>{metaTitle}</title>
        <meta name="description" content={metaDescription} />
        <meta property="og:title" content={metaTitle} />
        <meta property="og:description" content={metaDescription} />
      </Helmet>
      <NavBar />
      {/* <HeroContent
        title={data.title || data.name}
        description={
          data.description || data.short_description || data.overview
        }
        langStrings={langStrings || {}}
      >
        <BreadcrumbNav items={breadcrumbItems} />
      </HeroContent> */}
      <AskQuestionsBanner />
      <Box sx={{ pb: 6, pt: 3 }}>
        <Container>
          {firstHeading && (
            <Box
              sx={{
                maxWidth: "800px",
                wordWrap: "break-word",
                margin: "0 auto",
                overflowWrap: "break-word",
                "& *": {
                  wordWrap: "break-word",
                  overflowWrap: "break-word",
                  hyphens: "auto",
                },
              }}
            >
              <Typography
                variant="h1"
                sx={{
                  // marginTop: "2rem",
                  // marginBottom: "1rem",
                  fontSize: "2.2rem",
                  fontWeight: 700,
                  display: "flex",
                  alignItems: "flex-start",
                }}
              >
                {firstHeading}
              </Typography>
            </Box>
          )}
          {/* Author Created At Breadcrumbs */}
          <Box
            sx={{
              maxWidth: "800px",
              margin: "0 auto",
              mb: 2,
              display: "flex",
              alignItems: "center",
            }}
          >
            <Breadcrumbs
              style={{
                display: "flex",
                maxWidth: "640px",
                width: "100%",
                alignItems: "center",
                minHeight: "50px",
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <ShareButton
                  copiedText={
                    langStrings?.linkCopied || "Link copied to clipboard"
                  }
                />
                {/* <Typography sx={{ textDecoration: "none", color: "inherit" }}>
                  Author: John Doe
                </Typography> */}
              </Box>
            </Breadcrumbs>
            <Typography
              style={{
                textDecoration: "none",
                color: "#00000099",
                display: "flex",
                width: "100%",
                maxWidth: "160px",
              }}
              color="text.primary"
            >
              Created at:{new Date(data.meta.created_at).toLocaleDateString()}
            </Typography>
          </Box>

          <Widget />
          <Box
            sx={{
              maxWidth: "800px",
              wordWrap: "break-word",
              margin: "0 auto",
              overflowWrap: "break-word",
              "& *": {
                wordWrap: "break-word",
                overflowWrap: "break-word",
                hyphens: "auto",
              },
            }}
          >
            {children}
          </Box>
        </Container>
      </Box>
      {/* <Widget
        isAtBottom={true}
        title="Want a 1:1 answer for your situation?"
        description="Ask your question privately on August, your 24/7 personal AI health assistant."
      /> */}
      <AskQuestions />
      <Footer />
    </div>
  );
}
